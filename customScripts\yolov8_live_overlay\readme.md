# This is an example using yolov8 with a transparent overlay window

About the model:
- It uses yolov8
- It is compatible with pytorch, onnx, and tensorrt
- It uses multiprocessing and data streaming to enhance performance
- It doesn't do any aiming, that is up to you
- Ensure you pip install all the requirements, you can figure them out
- Try using pygame with your own overlay graphics like activation state or fps
- Overlays will not display on top of applications running in fullscreen mode